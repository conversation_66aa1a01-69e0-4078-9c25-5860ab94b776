package com.challenge.api.service;

import com.challenge.api.dto.CreateEmployeeRequest;
import com.challenge.api.dto.EmployeeResponse;
import com.challenge.api.error.NotFoundException;
import com.challenge.api.model.Employee;
import com.challenge.api.model.EmployeeImpl;
import com.challenge.api.store.EmployeeStore;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

/**
 * Implementation of EmployeeService.
 * Contains business logic for employee operations.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EmployeeServiceImpl implements EmployeeService {

    private final EmployeeStore employeeStore;

    @Override
    public List<EmployeeResponse> getAllEmployees() {
        log.info("Retrieving all employees");
        List<Employee> employees = employeeStore.findAll();
        log.info("Found {} employees", employees.size());
        
        return employees.stream()
                .map(EmployeeResponse::fromEmployee)
                .toList();
    }

    @Override
    public EmployeeResponse getEmployeeByUuid(UUID uuid) {
        log.info("Retrieving employee with UUID: {}", uuid);
        
        Employee employee = employeeStore.findByUuid(uuid)
                .orElseThrow(() -> {
                    log.warn("Employee not found with UUID: {}", uuid);
                    return new NotFoundException("Employee not found with UUID: " + uuid);
                });
        
        log.info("Found employee: {} {}", employee.getFirstName(), employee.getLastName());
        return EmployeeResponse.fromEmployee(employee);
    }

    @Override
    public EmployeeResponse createEmployee(CreateEmployeeRequest request) {
        log.info("Creating new employee: {} {}", request.getFirstName(), request.getLastName());
        
        // Validate required fields
        validateCreateEmployeeRequest(request);
        
        // Create new employee
        Employee employee = new EmployeeImpl(
                request.getFirstName(),
                request.getLastName(),
                request.getFullName(),
                request.getSalary(),
                request.getAge(),
                request.getJobTitle(),
                request.getEmail(),
                request.getContractHireDate() != null ? request.getContractHireDate() : Instant.now()
        );
        
        // Save employee (UUID will be generated in store)
        Employee savedEmployee = employeeStore.save(employee);
        
        log.info("Created employee with UUID: {}", savedEmployee.getUuid());
        return EmployeeResponse.fromEmployee(savedEmployee);
    }

    /**
     * Validate the create employee request
     */
    private void validateCreateEmployeeRequest(CreateEmployeeRequest request) {
        if (request.getFirstName() == null || request.getFirstName().trim().isEmpty()) {
            throw new IllegalArgumentException("First name is required");
        }
        if (request.getLastName() == null || request.getLastName().trim().isEmpty()) {
            throw new IllegalArgumentException("Last name is required");
        }
        if (request.getEmail() == null || request.getEmail().trim().isEmpty()) {
            throw new IllegalArgumentException("Email is required");
        }
        if (request.getJobTitle() == null || request.getJobTitle().trim().isEmpty()) {
            throw new IllegalArgumentException("Job title is required");
        }
        if (request.getSalary() == null || request.getSalary() <= 0) {
            throw new IllegalArgumentException("Valid salary is required");
        }
        if (request.getAge() == null || request.getAge() <= 0) {
            throw new IllegalArgumentException("Valid age is required");
        }
        
        // Set full name if not provided
        if (request.getFullName() == null || request.getFullName().trim().isEmpty()) {
            request.setFullName(request.getFirstName() + " " + request.getLastName());
        }
    }
}
