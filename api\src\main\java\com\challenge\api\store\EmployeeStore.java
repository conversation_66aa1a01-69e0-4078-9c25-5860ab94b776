package com.challenge.api.store;

import com.challenge.api.model.Employee;
import com.challenge.api.model.EmployeeImpl;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Thread-safe in-memory store for Employee data.
 * Uses ConcurrentHashMap for thread safety.
 */
@Component
public class EmployeeStore {

    private final ConcurrentHashMap<UUID, Employee> employees = new ConcurrentHashMap<>();

    public EmployeeStore() {
        // Initialize with some mock data
        initializeMockData();
    }

    /**
     * Save an employee to the store
     */
    public Employee save(Employee employee) {
        if (employee.getUuid() == null) {
            employee.setUuid(UUID.randomUUID());
        }
        employees.put(employee.getUuid(), employee);
        return employee;
    }

    /**
     * Find an employee by UUID
     */
    public Optional<Employee> findByUuid(UUID uuid) {
        return Optional.ofNullable(employees.get(uuid));
    }

    /**
     * Get all employees
     */
    public List<Employee> findAll() {
        return List.copyOf(employees.values());
    }

    /**
     * Check if an employee exists by UUID
     */
    public boolean existsByUuid(UUID uuid) {
        return employees.containsKey(uuid);
    }

    /**
     * Delete an employee by UUID
     */
    public boolean deleteByUuid(UUID uuid) {
        return employees.remove(uuid) != null;
    }

    /**
     * Get the count of employees
     */
    public int count() {
        return employees.size();
    }

    /**
     * Initialize the store with some mock employee data
     */
    private void initializeMockData() {
        // Create some sample employees
        Employee emp1 = new EmployeeImpl(
            "John", "Doe", "John Doe", 75000, 30, 
            "Software Engineer", "<EMAIL>", Instant.now().minusSeconds(365 * 24 * 60 * 60)
        );
        
        Employee emp2 = new EmployeeImpl(
            "Jane", "Smith", "Jane Smith", 85000, 28, 
            "Senior Software Engineer", "<EMAIL>", Instant.now().minusSeconds(2 * 365 * 24 * 60 * 60)
        );
        
        Employee emp3 = new EmployeeImpl(
            "Bob", "Johnson", "Bob Johnson", 95000, 35, 
            "Engineering Manager", "<EMAIL>", Instant.now().minusSeconds(3 * 365 * 24 * 60 * 60)
        );

        // Save the mock employees
        save(emp1);
        save(emp2);
        save(emp3);
    }
}
