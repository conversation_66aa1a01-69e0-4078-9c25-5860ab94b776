package com.challenge.api.dto;

import com.challenge.api.model.Employee;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.UUID;

/**
 * DTO for employee responses.
 * Provides a clean API boundary for employee data.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeResponse {

    private UUID uuid;
    private String firstName;
    private String lastName;
    private String fullName;
    private Integer salary;
    private Integer age;
    private String jobTitle;
    private String email;
    private Instant contractHireDate;
    private Instant contractTerminationDate;

    /**
     * Factory method to create EmployeeResponse from Employee entity
     */
    public static EmployeeResponse fromEmployee(Employee employee) {
        return new EmployeeResponse(
            employee.getUuid(),
            employee.getFirstName(),
            employee.getLastName(),
            employee.getFullName(),
            employee.getS<PERSON><PERSON>(),
            employee.getAge(),
            employee.getJobTitle(),
            employee.getEmail(),
            employee.getContractHireDate(),
            employee.getContractTerminationDate()
        );
    }
}
