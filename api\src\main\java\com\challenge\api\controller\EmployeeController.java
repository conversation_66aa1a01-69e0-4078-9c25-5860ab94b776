package com.challenge.api.controller;

import com.challenge.api.dto.CreateEmployeeRequest;
import com.challenge.api.dto.EmployeeResponse;
import com.challenge.api.service.EmployeeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

/**
 * REST Controller for Employee operations.
 * Provides endpoints for managing employees through the Employees-R-US integration.
 */
@RestController
@RequestMapping("/api/v1/employee")
@RequiredArgsConstructor
@Slf4j
public class EmployeeController {

    private final EmployeeService employeeService;

    /**
     * Get all employees
     * @return List of all employees
     */
    @GetMapping
    public ResponseEntity<List<EmployeeResponse>> getAllEmployees() {
        log.info("GET /api/v1/employee - Retrieving all employees");
        List<EmployeeResponse> employees = employeeService.getAllEmployees();
        log.info("Returning {} employees", employees.size());
        return ResponseEntity.ok(employees);
    }

    /**
     * Get an employee by UUID
     * @param uuid Employee UUID
     * @return Requested Employee if exists
     */
    @GetMapping("/{uuid}")
    public ResponseEntity<EmployeeResponse> getEmployeeByUuid(@PathVariable UUID uuid) {
        log.info("GET /api/v1/employee/{} - Retrieving employee by UUID", uuid);
        EmployeeResponse employee = employeeService.getEmployeeByUuid(uuid);
        return ResponseEntity.ok(employee);
    }

    /**
     * Create a new employee
     * @param request Employee creation request
     * @return Newly created Employee
     */
    @PostMapping
    public ResponseEntity<EmployeeResponse> createEmployee(@RequestBody CreateEmployeeRequest request) {
        log.info("POST /api/v1/employee - Creating new employee: {} {}",
                request.getFirstName(), request.getLastName());
        EmployeeResponse employee = employeeService.createEmployee(request);
        log.info("Created employee with UUID: {}", employee.getUuid());
        return ResponseEntity.status(HttpStatus.CREATED).body(employee);
    }
}
