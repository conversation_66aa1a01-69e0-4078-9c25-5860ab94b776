package com.challenge.api.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.UUID;

/**
 * Concrete implementation of the Employee interface.
 * Uses Lombok annotations for boilerplate code generation.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeImpl implements Employee {

    private UUID uuid;
    private String firstName;
    private String lastName;
    private String fullName;
    private Integer salary;
    private Integer age;
    private String jobTitle;
    private String email;
    private Instant contractHireDate;
    private Instant contractTerminationDate;

    /**
     * Constructor for creating a new employee (without UUID - will be set by service layer)
     */
    public EmployeeImpl(String firstName, String lastName, String fullName, Integer salary, 
                       Integer age, String jobTitle, String email, Instant contractHireDate) {
        this.firstName = firstName;
        this.lastName = lastName;
        this.fullName = fullName;
        this.salary = salary;
        this.age = age;
        this.jobTitle = jobTitle;
        this.email = email;
        this.contractHireDate = contractHireDate;
    }
}
