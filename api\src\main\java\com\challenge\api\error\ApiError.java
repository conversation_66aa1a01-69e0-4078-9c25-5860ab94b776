package com.challenge.api.error;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * Standard API error response structure.
 * Provides consistent error information across all endpoints.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiError {

    private String message;
    private int status;
    private String path;
    private Instant timestamp;

    public ApiError(String message, int status, String path) {
        this.message = message;
        this.status = status;
        this.path = path;
        this.timestamp = Instant.now();
    }
}
