package com.challenge.api.service;

import com.challenge.api.dto.CreateEmployeeRequest;
import com.challenge.api.dto.EmployeeResponse;

import java.util.List;
import java.util.UUID;

/**
 * Service interface for Employee operations.
 * Defines the contract for employee business logic.
 */
public interface EmployeeService {

    /**
     * Get all employees
     * @return List of all employees
     */
    List<EmployeeResponse> getAllEmployees();

    /**
     * Get an employee by UUID
     * @param uuid Employee UUID
     * @return Employee if found
     * @throws com.challenge.api.error.NotFoundException if employee not found
     */
    EmployeeResponse getEmployeeByUuid(UUID uuid);

    /**
     * Create a new employee
     * @param request Employee creation request
     * @return Created employee
     */
    EmployeeResponse createEmployee(CreateEmployeeRequest request);
}
